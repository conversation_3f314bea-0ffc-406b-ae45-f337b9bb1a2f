#include "qt_gui_ros2/mainWindow.hpp"

#include <QGridLayout>
#include <geometry_msgs/msg/point.hpp>
#include <rviz_common/view_manager.hpp>
#include <rviz_common/display_context.hpp>
#include <rviz_rendering/render_window.hpp>
#include <QVector3D>
#include <QDebug>
#include <rviz_common/tool_manager.hpp>
#include <rviz_common/view_manager.hpp>

MainWindow::MainWindow(QApplication *app, rviz_common::ros_integration::RosNodeAbstractionIface::WeakPtr rviz_ros_node, QWidget *parent)
    : QMainWindow(parent), app_(app), rviz_ros_node_(rviz_ros_node), mapReceived_(false) {

    // 设置窗口标题和大小
    setWindowTitle("RobotCar Navigation Control");
    resize(1200, 800);

    // 设置状态栏
    statusBar()->showMessage("初始化中...");

    qDebug() << "Qt GUI初始化开始";

    try {
        mainLayout_ = new QVBoxLayout;
        centralWidget_ = new QWidget();

    initializeRViz();
    setupJoystickControls();

    // Add frame input box and button
    QLabel *frameLabel = new QLabel("Reference Frame:");
    frameLineEdit_ = new QLineEdit("map");
    QPushButton *updateFrameButton = new QPushButton("Update Frame");
    connect(updateFrameButton, &QPushButton::clicked, this, &MainWindow::updateFrame);

    mainLayout_->addWidget(frameLabel);
    mainLayout_->addWidget(frameLineEdit_);
    mainLayout_->addWidget(updateFrameButton);
    mainLayout_->addWidget(renderPanel_);  // Add the render panel here

        // Initialize cmd_vel publisher for robotcar system
        // 使用robotcar系统的差分驱动控制器话题
        cmdVelPublisher_ = rviz_ros_node_.lock()->get_raw_node()->create_publisher<geometry_msgs::msg::Twist>("/diff_drive_controller/cmd_vel_unstamped", 10);
        qDebug() << "cmd_vel发布器初始化成功: /diff_drive_controller/cmd_vel_unstamped";

    // Set up the indicator for map reception
    mapReceivedIndicator_ = new QLabel("Map Received: No", this);
    mapReceivedIndicator_->setStyleSheet("color: red;");
    mainLayout_->addWidget(mapReceivedIndicator_);

    centralWidget_->setLayout(mainLayout_);
    setCentralWidget(centralWidget_);
    
    
    QString frame_id = frameLineEdit_->text();
    // Update the fixed frame in VisualizationManager
    manager_->getRootDisplayGroup()->setFixedFrame(frame_id); // Set for root display group
    manager_->setFixedFrame(frame_id); // Set for frame manager

    // Call updateFixedFrame() to apply changes across the visualization manager
    //manager_->updateFixedFrame();

        setupGridDisplay();
        setupTFDisplay();
        setupMapDisplay();
        setupRobotModelDisplay();
        setupLaserScanDisplay();
        setupMapSubscriber();

        // 检查关键话题是否可用
        checkSystemStatus();

        // 更新状态栏
        statusBar()->showMessage("初始化完成，等待数据...");
        qDebug() << "Qt GUI初始化完成";
    } catch (const std::exception& e) {
        qDebug() << "初始化过程中发生错误: " << e.what();
        statusBar()->showMessage("初始化失败: " + QString(e.what()));
        QMessageBox::critical(this, "初始化错误",
                             "Qt GUI初始化过程中发生错误:\n" + QString(e.what()) +
                             "\n\n请检查ROS系统是否正常运行，并重新启动应用程序。");
    }
}
// {{ AURA-X: Add - 实现系统状态检查函数. Approval: 寸止(ID:1735659700). }}
void MainWindow::checkSystemStatus() {
    try {
        auto node = rviz_ros_node_.lock()->get_raw_node();
        auto topics = node->get_topic_names_and_types();

        // 检查关键话题是否存在
        bool robot_description_found = false;
        bool merged_scan_found = false;
        bool map_found = false;
        bool odom_found = false;

        for (const auto& topic : topics) {
            if (topic.first == "/robot_description") {
                robot_description_found = true;
                qDebug() << "✓ 找到机器人描述话题: /robot_description";
            } else if (topic.first == "/merged") {
                merged_scan_found = true;
                qDebug() << "✓ 找到融合激光雷达话题: /merged";
            } else if (topic.first == "/map") {
                map_found = true;
                qDebug() << "✓ 找到地图话题: /map";
            } else if (topic.first == "/odom") {
                odom_found = true;
                qDebug() << "✓ 找到里程计话题: /odom";
            }
        }

        // 报告缺失的话题
        if (!robot_description_found) {
            qDebug() << "⚠ 警告: 未找到机器人描述话题 /robot_description";
        }
        if (!merged_scan_found) {
            qDebug() << "⚠ 警告: 未找到融合激光雷达话题 /merged";
        }
        if (!map_found) {
            qDebug() << "⚠ 警告: 未找到地图话题 /map";
        }
        if (!odom_found) {
            qDebug() << "⚠ 警告: 未找到里程计话题 /odom";
        }

        qDebug() << "系统状态检查完成";

    } catch (const std::exception& e) {
        qDebug() << "系统状态检查失败:" << e.what();
    }
}

MainWindow::~MainWindow() {
    // 不在析构函数中调用rclcpp::shutdown()，避免重复关闭
    // shutdown将在main函数中统一处理
    qDebug() << "MainWindow析构函数调用";
}

QWidget *MainWindow::getParentWindow() {
    return this;
}

rviz_common::PanelDockWidget *MainWindow::addPane(const QString &name, QWidget *pane, Qt::DockWidgetArea area, bool floating) {
    return nullptr;
}

void MainWindow::setStatus(const QString &message) {
    // Optional: handle setting a status message here
}

void MainWindow::initializeRViz() {
    app_->processEvents();
    renderPanel_ = new rviz_common::RenderPanel(centralWidget_);
    app_->processEvents();
    renderPanel_->getRenderWindow()->initialize();

    auto clock = rviz_ros_node_.lock()->get_raw_node()->get_clock();
    manager_ = new rviz_common::VisualizationManager(renderPanel_, rviz_ros_node_, this, clock);
    renderPanel_->initialize(manager_);

    // Enable mouse tracking and focus policy to ensure it receives events
    renderPanel_->setMouseTracking(true);
    renderPanel_->setFocusPolicy(Qt::StrongFocus);

    app_->processEvents();
    manager_->initialize();
    manager_->startUpdate();

    // Set the view controller to Orbit to allow for mouse interactions
    manager_->getViewManager()->setCurrentViewControllerType("rviz_default_plugins/Orbit");

    // Retrieve the active view controller to set properties and confirm it's set up correctly
    auto orbit_view_controller = manager_->getViewManager()->getCurrent();
    if (!orbit_view_controller) {
        qDebug() << "Orbit view controller could not be set.";
        return;
    }

    qDebug() << "Orbit view controller initialized successfully.";

    // Set default distance and focal point for the camera
    orbit_view_controller->subProp("Distance")->setValue(10.0);
    orbit_view_controller->subProp("Focal Point")->setValue(QVariant::fromValue(QVector3D(0.0, 0.0, 0.0)));

    // Set initial orientation of the camera
    orbit_view_controller->subProp("Pitch")->setValue(1.5708);  // Example angle in radians
    orbit_view_controller->subProp("Yaw")->setValue(3.14);     // Example angle in radians

    // Set Interact tool as the active tool to enable mouse interactions
    auto tool_manager = manager_->getToolManager();
    tool_manager->setCurrentTool(tool_manager->addTool("rviz_default_plugins/Interact"));
}

void MainWindow::setupGridDisplay() {
    QString frame_id = frameLineEdit_->text();

    // Initialize the grid display
    grid_ = manager_->createDisplay("rviz_default_plugins/Grid", "Grid", true);
    if (grid_) {
        // {{ AURA-X: Modify - 优化网格显示配置，适配深色主题. Approval: 寸止(ID:1735659700). }}
        grid_->subProp("Line Style")->setValue("Lines");
        grid_->subProp("Color")->setValue(QColor(160, 160, 164));  // 使用灰色，适配深色主题
        grid_->subProp("Alpha")->setValue(0.5);  // 设置透明度
        grid_->subProp("Cell Count")->setValue(50);  // 设置网格数量
        grid_->subProp("Cell Size")->setValue(1.0);  // 设置网格大小
        grid_->subProp("Reference Frame")->setValue(frame_id);
        qDebug() << "Grid display configured for fixed frame:" << frame_id;
    } else {
        qDebug() << "Failed to create Grid display.";
    }
}

void MainWindow::setupTFDisplay() {
    // Set up the TF display to show frames with a fixed frame
    tf_display_ = manager_->createDisplay("rviz_default_plugins/TF", "TF Display", true);
    if (tf_display_) {
        // {{ AURA-X: Modify - 优化TF显示配置，提供更好的坐标系可视化. Approval: 寸止(ID:1735659700). }}
        tf_display_->subProp("Show Axes")->setValue(true);
        tf_display_->subProp("Show Names")->setValue(true);
        tf_display_->subProp("Show Arrows")->setValue(true);
        tf_display_->subProp("Marker Scale")->setValue(0.3);  // 设置标记大小
        tf_display_->subProp("Update Interval")->setValue(0.1);  // 设置更新间隔
        qDebug() << "TF display configured with axes, names and arrows shown.";
    } else {
        qDebug() << "Failed to create TF display.";
    }
}

void MainWindow::setupMapDisplay() {
    QString frame_id = frameLineEdit_->text();

    // Set up the Map display for the /map topic
    map_display_ = manager_->createDisplay("rviz_default_plugins/Map", "Map Display", true);
    if (map_display_) {
        map_display_->subProp("Topic")->setValue("/map");
        map_display_->subProp("Alpha")->setValue(1.0);
        map_display_->subProp("Draw Behind")->setValue(false);
        map_display_->subProp("Color Scheme")->setValue("map");
        map_display_->subProp("Topic")->subProp("Durability Policy")->setValue("Transient Local");
        
        
        
        //map_display_->setEnabled(true);

        qDebug() << "Map display configured for /map topic with fixed frame:" << frame_id;
    } else {
        qDebug() << "Failed to create Map display.";
    }
}



void MainWindow::setupRobotModelDisplay() {
    // Set up the RobotModel display for the /robot_description topic
    robot_model_display_ = manager_->createDisplay("rviz_default_plugins/RobotModel", "RobotModel Display", true);
    if (robot_model_display_) {
        // 尝试使用多个可能的机器人描述话题
        try {
            // 首先检查是否有/robot_description话题
            auto node = rviz_ros_node_.lock()->get_raw_node();
            auto topics = node->get_topic_names_and_types();

            QString robot_description_topic = "/robot_description"; // 默认话题

            // 查找可用的robot_description话题
            for (const auto& topic : topics) {
                if (topic.first.find("robot_description") != std::string::npos) {
                    robot_description_topic = QString::fromStdString(topic.first);
                    qDebug() << "找到机器人描述话题:" << robot_description_topic;
                    break;
                }
            }

            // 设置找到的话题
            robot_model_display_->subProp("Description Topic")->setValue(robot_description_topic);
            qDebug() << "使用机器人描述话题:" << robot_description_topic;
        } catch (const std::exception& e) {
            qDebug() << "设置机器人模型时出错:" << e.what();
            // 回退到默认话题
            robot_model_display_->subProp("Description Topic")->setValue("/robot_description");
        }

        robot_model_display_->subProp("TF Prefix")->setValue("");  // 设置TF前缀为空
        robot_model_display_->subProp("Visual Enabled")->setValue(true);  // 启用视觉模型
        robot_model_display_->subProp("Collision Enabled")->setValue(false);  // 禁用碰撞模型
        robot_model_display_->subProp("Alpha")->setValue(1.0);  // 设置透明度
        qDebug() << "RobotModel display configured for /robot_description topic.";
    } else {
        qDebug() << "Failed to create RobotModel display.";
    }
}

void MainWindow::setupJoystickControls() {
    QGridLayout *joystickLayout = new QGridLayout;

    forwardButton_ = new QPushButton("Forward");
    backwardButton_ = new QPushButton("Backward");
    leftButton_ = new QPushButton("Left");
    rightButton_ = new QPushButton("Right");
    stopButton_ = new QPushButton("Stop");

    joystickLayout->addWidget(forwardButton_, 0, 1);
    joystickLayout->addWidget(backwardButton_, 2, 1);
    joystickLayout->addWidget(leftButton_, 1, 0);
    joystickLayout->addWidget(rightButton_, 1, 2);
    joystickLayout->addWidget(stopButton_, 1, 1);

    mainLayout_->addLayout(joystickLayout);

    // Connect buttons to send appropriate cmd_vel messages
    connect(forwardButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 1.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
    connect(backwardButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = -1.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
    connect(leftButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = 1.0;
        sendJoystickCommand();
    });
    connect(rightButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = -1.0;
        sendJoystickCommand();
    });
    connect(stopButton_, &QPushButton::pressed, this, [this]() {
        currentTwist_.linear.x = 0.0; currentTwist_.angular.z = 0.0;
        sendJoystickCommand();
    });
}

void MainWindow::sendJoystickCommand() {
    cmdVelPublisher_->publish(currentTwist_);
}

void MainWindow::updateFrame() {
    QString frame_id = frameLineEdit_->text();

    // Update the grid display's reference frame
    if (grid_) {
        grid_->subProp("Reference Frame")->setValue(frame_id);
    }

    // Set the fixed frame in the FrameManager directly
    if (manager_ && manager_->getFrameManager()) {
        manager_->setFixedFrame(frame_id); // Set for frame manager
        manager_->getRootDisplayGroup()->setFixedFrame(frame_id); // Set for root display group
        qDebug() << "FrameManager fixed frame updated to:" << frame_id;
    }
}

void MainWindow::closeEvent(QCloseEvent *event) {
    // 不在closeEvent中调用rclcpp::shutdown()，避免重复关闭
    // shutdown将在main函数中统一处理
    qDebug() << "应用程序窗口关闭";
    event->accept();
}

void MainWindow::setupMapSubscriber() {
    auto node = rviz_ros_node_.lock()->get_raw_node();
    mapSubscriber_ = node->create_subscription<nav_msgs::msg::OccupancyGrid>(
        "/map", 10,
        [this](const nav_msgs::msg::OccupancyGrid::SharedPtr msg) {
            Q_UNUSED(msg);
            mapReceived_ = true;
            qDebug() << "Map Received";
            updateMapReceivedIndicator(true);

            // Enable map display if map data is received
            /*if (map_display_) {
                map_display_->setEnabled(true);
            }*/
        }
    );

    // Set a timer to reset the indicator if no map data is received for a while
    auto timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [this]() {
        // 检查是否有地图数据
        bool hasMapData = false;
        try {
            // 尝试获取最新的地图消息
            auto node = rviz_ros_node_.lock()->get_raw_node();
            auto map_info = node->get_topic_names_and_types();
            for (const auto& topic : map_info) {
                if (topic.first == "/map") {
                    hasMapData = true;
                    break;
                }
            }

            // 更新地图接收状态
            mapReceived_ = hasMapData;
            updateMapReceivedIndicator(hasMapData);

            // 如果有地图数据但显示未启用，则启用地图显示
            if (hasMapData && map_display_ && !map_display_->isEnabled()) {
                map_display_->setEnabled(true);
                qDebug() << "Map display enabled";
            }
        } catch (const std::exception& e) {
            qDebug() << "Error checking map status:" << e.what();
        }
    });
    timer->start(2000);  // Check every 2 seconds
}

void MainWindow::updateMapReceivedIndicator(bool received) {
    if (received) {
        mapReceivedIndicator_->setText("Map Received: Yes");
        mapReceivedIndicator_->setStyleSheet("color: green;");
    } else {
        mapReceivedIndicator_->setText("Map Received: No");
        mapReceivedIndicator_->setStyleSheet("color: red;");
    }
}

// Set up LaserScan Display
void MainWindow::setupLaserScanDisplay() {
    auto laser_scan_display = manager_->createDisplay("rviz_default_plugins/LaserScan", "LaserScan Display", true);
    if (laser_scan_display) {
        laser_scan_display->subProp("Topic")->setValue("/merged");     // 使用融合后的激光雷达数据
        laser_scan_display->subProp("Size (m)")->setValue(0.1);       // 调整点大小以获得更好的显示效果
        laser_scan_display->subProp("Color")->setValue(QColor(Qt::green));  // 设置激光点颜色为绿色
        laser_scan_display->subProp("Style")->setValue("Points");      // 设置显示样式为点
        laser_scan_display->subProp("Alpha")->setValue(0.8);           // 设置透明度
        qDebug() << "LaserScan display configured successfully for /merged topic.";
    } else {
        qDebug() << "Failed to configure LaserScan display.";
    }
}
