#!/usr/bin/env python3
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """
    生成robotcar Qt GUI的launch描述
    """
    
    # 声明launch参数
    use_sim_time_arg = DeclareLaunchArgument( 
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )
    
    frame_id_arg = DeclareLaunchArgument(
        'frame_id',
        default_value='map',
        description='RViz显示的参考坐标系'
    )
    
    robot_base_frame_arg = DeclareLaunchArgument(
        'robot_base_frame',
        default_value='base_footprint',
        description='机器人本体坐标系'
    )

    # {{ AURA-X: Add - 添加调试模式参数. Approval: 寸止(ID:1735659700). }}
    debug_mode_arg = DeclareLaunchArgument(
        'debug_mode',
        default_value='false',
        description='启用调试模式，输出详细日志信息'
    )
    
    # Qt GUI节点
    qt_gui_node = Node(
        package='qt_gui_ros2',
        executable='qt_gui_ros2',
        name='robotcar_qt_gui',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'frame_id': LaunchConfiguration('frame_id'),
            'robot_base_frame': LaunchConfiguration('robot_base_frame'),
            'debug_mode': LaunchConfiguration('debug_mode'),  # {{ AURA-X: Add - 添加调试模式参数. }}
        }],
        # 重映射话题以适配robotcar系统
        remappings=[
            # 控制话题重映射
            ('/cmd_vel', '/diff_drive_controller/cmd_vel_unstamped'),
            # 里程计话题重映射 (使用EKF融合后的高质量数据)
            ('/odom', '/odom'),
            # 激光雷达话题 - 移除重映射，直接在代码中使用/merged
            # ('/scan', '/merged'),  # 注释掉，改为在代码中直接使用/merged
            # 地图话题 (保持默认)
            ('/map', '/map'),
            # IMU话题
            ('/imu', '/imu_sensor_broadcaster/imu'),  # 修正IMU话题路径
        ]
    )
    
    # 启动信息
    start_info = LogInfo(
        msg=[
            '\n',
            '========================================\n',
            '  RobotCar 智能机器人 GUI 启动中...\n',
            '========================================\n',
            '功能说明:\n',
            '- RViz集成可视化界面\n',
            '- 虚拟摇杆机器人控制\n',
            '- 地图和传感器数据显示\n',
            '- 坐标系管理\n',
            '\n',
            '控制说明:\n',
            '- 前进/后退/左转/右转按钮控制机器人\n',
            '- 停止按钮立即停止机器人\n',
            '- 参考坐标系可在界面中修改\n',
            '\n',
            '话题映射:\n',
            '- 控制: /diff_drive_controller/cmd_vel_unstamped\n',
            '- 里程计: /odom (EKF融合后数据)\n',
            '- 激光雷达: /merged (双雷达融合数据)\n',
            '- 地图: /map\n',
            '- IMU: /imu_sensor_broadcaster/imu\n',
            '========================================\n'
        ]
    )
    
    return LaunchDescription([
        # Launch参数
        use_sim_time_arg,
        frame_id_arg,
        robot_base_frame_arg,
        debug_mode_arg,  # {{ AURA-X: Add - 添加调试模式参数到LaunchDescription. }}
        # 启动信息
        start_info,
        # 节点
        qt_gui_node,
    ])
