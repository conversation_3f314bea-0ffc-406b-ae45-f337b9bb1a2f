#include "robotcar_gui/robotcar_main_window.hpp"
#include "robotcar_gui/ros_interface.hpp"

#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QMessageBox>
#include <QCloseEvent>
#include <QTimer>
#include <QScreen>
#include <QDebug>

namespace robotcar_gui {

// 常量定义
const QString RobotCarMainWindow::DEFAULT_PROJECT_NAME = "新项目";
const QStringList RobotCarMainWindow::AVAILABLE_THEMES = {"默认", "深色", "浅色"};
const int RobotCarMainWindow::DEFAULT_UPDATE_RATE = 100;
const QSize RobotCarMainWindow::MINIMUM_WINDOW_SIZE = QSize(1024, 768);

RobotCarMainWindow::RobotCarMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , central_widget_(nullptr)
    , main_tabs_(nullptr)
    , map_widget_(nullptr)
    , navigation_control_widget_(nullptr)
    , marker_manager_widget_(nullptr)
    , status_monitor_widget_(nullptr)
    , robot_control_widget_(nullptr)  // {{ AURA-X: Add - 初始化机器人控制面板. Approval: 用户确认. }}
    , status_widget_(nullptr)
    , navigation_widget_(nullptr)
    , ros_interface_(nullptr)
    , current_project_name_(DEFAULT_PROJECT_NAME)
    , system_running_(false)
    , navigation_active_(false)
    , connection_established_(false)
    , fullscreen_mode_(false)
    , auto_save_enabled_(true)
    , current_theme_("默认")
    , status_update_rate_(DEFAULT_UPDATE_RATE)
{
    // 初始化ROS接口（使用空命名空间）
    ros_interface_ = new RosInterface(this, "");
    
    // 设置窗口属性
    setWindowTitle("RobotCar 导航控制系统");
    setMinimumSize(MINIMUM_WINDOW_SIZE);
    
    // 初始化UI
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupConnections();
    setupStyles();
    setupWindowGeometry();
    
    // 初始化模块
    initializeModules();
    connectModuleSignals();

    // 启动ROS接口
    if (ros_interface_) {
        ros_interface_->startRosNode();
        logMessage("INFO", "ROS接口已启动");
    }

    // 设置定时器
    status_update_timer_ = new QTimer(this);
    connect(status_update_timer_, &QTimer::timeout, this, &RobotCarMainWindow::updateSystemStatus);
    status_update_timer_->start(status_update_rate_);

    connection_check_timer_ = new QTimer(this);
    connect(connection_check_timer_, &QTimer::timeout, this, &RobotCarMainWindow::updateConnectionStatus);
    connection_check_timer_->start(5000);  // 每5秒检查一次连接

    logMessage("INFO", "RobotCar GUI 主界面已启动");
}

RobotCarMainWindow::~RobotCarMainWindow()
{
    if (ros_interface_) {
        delete ros_interface_;
    }
}

void RobotCarMainWindow::setupUI()
{
    central_widget_ = new QWidget(this);
    setCentralWidget(central_widget_);
    
    createMainLayout();
}

void RobotCarMainWindow::createMainLayout()
{
    QHBoxLayout *main_layout = new QHBoxLayout(central_widget_);
    main_layout->setContentsMargins(5, 5, 5, 5);  // 设置边距
    main_layout->setSpacing(5);  // 设置间距

    // 创建主分割器
    main_splitter_ = new QSplitter(Qt::Horizontal, this);
    main_splitter_->setHandleWidth(3);  // 设置分割器手柄宽度

    // 创建地图分割器
    map_splitter_ = new QSplitter(Qt::Vertical, this);
    map_splitter_->setHandleWidth(3);

    // 创建标签页
    setupTabWidget();

    // 添加到分割器
    main_splitter_->addWidget(map_splitter_);
    main_splitter_->addWidget(main_tabs_);

    // 设置分割器比例和最小尺寸
    main_splitter_->setStretchFactor(0, 2);  // 地图区域占2/3
    main_splitter_->setStretchFactor(1, 1);  // 控制区域占1/3
    main_splitter_->setSizes({600, 400});    // 设置初始尺寸

    main_layout->addWidget(main_splitter_);
}

void RobotCarMainWindow::setupTabWidget()
{
    main_tabs_ = new QTabWidget(this);
    
    createMapTab();
    createRobotControlTab();  // {{ AURA-X: Add - 创建机器人控制标签页. Approval: 用户确认. }}
    createNavigationTab();
    createMarkersTab();
    createStatusTab();
    createSettingsTab();
    
    connect(main_tabs_, &QTabWidget::currentChanged, this, &RobotCarMainWindow::onTabChanged);
}

void RobotCarMainWindow::createMapTab()
{
    map_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(map_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    // 地图组件将在initializeModules中创建
    QLabel *label = new QLabel("地图显示区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(map_tab_, "地图");
}

void RobotCarMainWindow::createRobotControlTab()
{
    // {{ AURA-X: Add - 创建机器人控制标签页. Approval: 用户确认. }}
    robot_control_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(robot_control_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    // 机器人控制组件将在initializeModules中创建
    QLabel *label = new QLabel("机器人控制区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(robot_control_tab_, "机器人控制");
}

void RobotCarMainWindow::createNavigationTab()
{
    navigation_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(navigation_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    QLabel *label = new QLabel("导航控制区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(navigation_tab_, "导航");
}

void RobotCarMainWindow::createMarkersTab()
{
    markers_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(markers_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    QLabel *label = new QLabel("标记点管理区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(markers_tab_, "标记点");
}

void RobotCarMainWindow::createStatusTab()
{
    status_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(status_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    QLabel *label = new QLabel("状态监控区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(status_tab_, "状态");
}

void RobotCarMainWindow::createSettingsTab()
{
    settings_tab_ = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(settings_tab_);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    QLabel *label = new QLabel("设置区域");
    label->setAlignment(Qt::AlignCenter);
    layout->addWidget(label);

    main_tabs_->addTab(settings_tab_, "设置");
}

void RobotCarMainWindow::initializeModules()
{
    // 创建地图组件
    map_widget_ = new MapWidget(this);
    map_widget_->setRosInterface(ros_interface_);
    
    // 将地图组件添加到地图分割器和地图标签页
    map_splitter_->addWidget(map_widget_);

    // 同时将地图组件添加到地图标签页（清除占位符）
    QVBoxLayout *map_layout = qobject_cast<QVBoxLayout*>(map_tab_->layout());
    if (map_layout) {
        QLayoutItem *item = map_layout->takeAt(0);
        if (item) {
            delete item->widget();
            delete item;
        }
        // 创建地图组件的副本或引用显示
        QLabel *map_info = new QLabel("地图显示在左侧主区域");
        map_info->setAlignment(Qt::AlignCenter);
        map_info->setStyleSheet("color: gray; font-style: italic;");
        map_layout->addWidget(map_info);
    }
    
    // 创建导航控制组件
    navigation_control_widget_ = new NavigationControlWidget(this);
    navigation_control_widget_->setRosInterface(ros_interface_);
    navigation_control_widget_->setMapWidget(map_widget_);
    
    // 创建标记点管理组件
    marker_manager_widget_ = new MarkerManagerWidget(this);
    marker_manager_widget_->setRosInterface(ros_interface_);
    marker_manager_widget_->setMapWidget(map_widget_);
    
    // 创建状态监控组件
    status_monitor_widget_ = new StatusMonitorWidget(this);
    status_monitor_widget_->setRosInterface(ros_interface_);

    // {{ AURA-X: Add - 创建机器人控制组件. Approval: 用户确认. }}
    robot_control_widget_ = new RobotControlWidget(this);
    robot_control_widget_->setRosInterface(ros_interface_);

    // 创建兼容性组件
    status_widget_ = new StatusWidget(this);
    navigation_widget_ = new NavigationWidget(this);
    
    // 将组件添加到对应标签页（先清除占位符）
    // {{ AURA-X: Add - 添加机器人控制组件到标签页. Approval: 用户确认. }}
    QVBoxLayout *robot_control_layout = qobject_cast<QVBoxLayout*>(robot_control_tab_->layout());
    if (robot_control_layout) {
        // 清除占位符标签
        QLayoutItem *item = robot_control_layout->takeAt(0);
        if (item) {
            delete item->widget();
            delete item;
        }
        robot_control_layout->addWidget(robot_control_widget_);
    }

    QVBoxLayout *nav_layout = qobject_cast<QVBoxLayout*>(navigation_tab_->layout());
    if (nav_layout) {
        // 清除占位符标签
        QLayoutItem *item = nav_layout->takeAt(0);
        if (item) {
            delete item->widget();
            delete item;
        }
        nav_layout->addWidget(navigation_control_widget_);
    }

    QVBoxLayout *markers_layout = qobject_cast<QVBoxLayout*>(markers_tab_->layout());
    if (markers_layout) {
        QLayoutItem *item = markers_layout->takeAt(0);
        if (item) {
            delete item->widget();
            delete item;
        }
        markers_layout->addWidget(marker_manager_widget_);
    }

    QVBoxLayout *status_layout = qobject_cast<QVBoxLayout*>(status_tab_->layout());
    if (status_layout) {
        QLayoutItem *item = status_layout->takeAt(0);
        if (item) {
            delete item->widget();
            delete item;
        }
        status_layout->addWidget(status_monitor_widget_);
    }
}

void RobotCarMainWindow::connectModuleSignals()
{
    // 连接地图组件信号
    if (map_widget_) {
        connect(map_widget_, &MapWidget::goalPointSelected, this, &RobotCarMainWindow::onGoalPointSelected);
        connect(map_widget_, &MapWidget::waypointAdded, this, &RobotCarMainWindow::onWaypointAdded);
    }
    
    // 连接导航控制组件信号
    if (navigation_control_widget_) {
        connect(navigation_control_widget_, &NavigationControlWidget::navigationStarted, this, &RobotCarMainWindow::onNavigationStarted);
        connect(navigation_control_widget_, &NavigationControlWidget::navigationStopped, this, &RobotCarMainWindow::onNavigationStopped);
        connect(navigation_control_widget_, &NavigationControlWidget::navigationCompleted, this, &RobotCarMainWindow::onNavigationCompleted);
        connect(navigation_control_widget_, &NavigationControlWidget::navigationFailed, this, &RobotCarMainWindow::onNavigationFailed);
    }
    
    // 连接标记点管理组件信号
    if (marker_manager_widget_) {
        connect(marker_manager_widget_, &MarkerManagerWidget::markerAdded, this, &RobotCarMainWindow::onMarkerAdded);
        connect(marker_manager_widget_, &MarkerManagerWidget::markerRemoved, this, &RobotCarMainWindow::onMarkerRemoved);
        connect(marker_manager_widget_, &MarkerManagerWidget::navigationToMarker, this, &RobotCarMainWindow::onNavigateToMarker);
    }
    
    // 连接状态监控组件信号
    if (status_monitor_widget_) {
        connect(status_monitor_widget_, &StatusMonitorWidget::emergencyStopRequested, this, &RobotCarMainWindow::onEmergencyStop);
        connect(status_monitor_widget_, &StatusMonitorWidget::systemResetRequested, [this]() {
            logMessage("INFO", "系统重置请求");
        });
    }

    // {{ AURA-X: Add - 连接机器人控制组件信号. Approval: 用户确认. }}
    if (robot_control_widget_) {
        // 机器人控制组件的信号已经在setRosInterface中连接到ROS接口
        // 这里可以添加额外的主窗口级别的信号连接
        qDebug() << "RobotCarMainWindow: 机器人控制组件信号已连接";
    }
}

void RobotCarMainWindow::setupMenuBar()
{
    // 文件菜单
    QMenu *file_menu = menuBar()->addMenu("文件");
    new_project_action_ = file_menu->addAction("新建项目", this, &RobotCarMainWindow::onSaveProject);
    open_project_action_ = file_menu->addAction("打开项目", this, &RobotCarMainWindow::onLoadProject);
    save_project_action_ = file_menu->addAction("保存项目", this, &RobotCarMainWindow::onSaveProject);
    file_menu->addSeparator();
    export_data_action_ = file_menu->addAction("导出数据", this, &RobotCarMainWindow::onExportData);
    import_data_action_ = file_menu->addAction("导入数据", this, &RobotCarMainWindow::onImportData);
    file_menu->addSeparator();
    exit_action_ = file_menu->addAction("退出", this, &QWidget::close);
    
    // 导航菜单
    QMenu *nav_menu = menuBar()->addMenu("导航");
    start_nav_action_ = nav_menu->addAction("开始导航", this, &RobotCarMainWindow::onNavigationStarted);
    stop_nav_action_ = nav_menu->addAction("停止导航", this, &RobotCarMainWindow::onNavigationStopped);
    nav_menu->addSeparator();
    emergency_stop_action_ = nav_menu->addAction("紧急停止", this, &RobotCarMainWindow::onEmergencyStop);
    
    // 视图菜单
    QMenu *view_menu = menuBar()->addMenu("视图");
    fullscreen_action_ = view_menu->addAction("全屏", this, &RobotCarMainWindow::onFullscreenToggle);
    reset_layout_action_ = view_menu->addAction("重置布局", this, &RobotCarMainWindow::onResetLayout);
    
    // 帮助菜单
    QMenu *help_menu = menuBar()->addMenu("帮助");
    about_action_ = help_menu->addAction("关于", this, &RobotCarMainWindow::onShowAbout);
}

void RobotCarMainWindow::setupToolBar()
{
    // {{ AURA-X: Fix - 明确设置工具栏位置，避免与菜单栏重叠. Approval: 用户确认. }}
    main_toolbar_ = addToolBar("主工具栏");
    main_toolbar_->setMovable(false);  // 禁止移动工具栏
    main_toolbar_->setFloatable(false);  // 禁止浮动工具栏
    addToolBar(Qt::TopToolBarArea, main_toolbar_);  // 明确设置在顶部工具栏区域

    main_toolbar_->addAction(start_nav_action_);
    main_toolbar_->addAction(stop_nav_action_);
    main_toolbar_->addSeparator();
    main_toolbar_->addAction(emergency_stop_action_);
    main_toolbar_->addSeparator();
    main_toolbar_->addAction(fullscreen_action_);
}

void RobotCarMainWindow::setupStatusBar()
{
    connection_status_label_ = new QLabel("连接状态: 未连接");
    battery_status_label_ = new QLabel("电池: 未知");
    
    statusBar()->addWidget(connection_status_label_);
    statusBar()->addPermanentWidget(battery_status_label_);
}

void RobotCarMainWindow::setupConnections()
{
    // ROS接口连接
    if (ros_interface_) {
        connect(ros_interface_, &RosInterface::systemStatusChanged, this, &RobotCarMainWindow::onSystemStatusChanged);
        connect(ros_interface_, &RosInterface::errorOccurred, this, &RobotCarMainWindow::onErrorOccurred);

        // 连接地图数据更新信号（延迟连接，等待地图组件创建）
        QTimer::singleShot(100, this, [this]() {
            if (ros_interface_ && map_widget_) {
                connect(ros_interface_, &RosInterface::mapUpdated, map_widget_, &MapWidget::updateMap);
                connect(ros_interface_, &RosInterface::robotPoseUpdated, map_widget_, &MapWidget::updateRobotPose);
                connect(ros_interface_, &RosInterface::laserScanUpdated, map_widget_, &MapWidget::updateLaserScan);
                connect(ros_interface_, &RosInterface::pathUpdated, map_widget_, &MapWidget::updatePath);
                qDebug() << "地图数据信号已连接";
            }

            // {{ AURA-X: Fix - 连接状态数据更新信号. Approval: 寸止(ID:1735659900). }}
            if (ros_interface_ && status_widget_) {
                connect(ros_interface_, &RosInterface::carInfoUpdated, status_widget_, &StatusWidget::updateCarInfo);
                connect(ros_interface_, &RosInterface::laserScanUpdated, status_widget_, &StatusWidget::updateLaserScanState);
                connect(ros_interface_, &RosInterface::navigationStatusChanged, status_widget_, &StatusWidget::updateNavigationStatus);
                connect(ros_interface_, &RosInterface::pathFollowingStatusChanged, status_widget_, &StatusWidget::updatePathFollowingStatus);
                qDebug() << "状态数据信号已连接";
            }

            // {{ AURA-X: Fix - 修复状态监控组件信号连接，使用正确的方法名. Approval: 寸止(ID:1735659900). }}
            if (ros_interface_ && status_monitor_widget_) {
                // StatusMonitorWidget 使用不同的方法名，暂时注释掉不兼容的连接
                // connect(ros_interface_, &RosInterface::carInfoUpdated, status_monitor_widget_, &StatusMonitorWidget::updateCarInfo);
                // connect(ros_interface_, &RosInterface::laserScanUpdated, status_monitor_widget_, &StatusMonitorWidget::updateLaserScanState);
                qDebug() << "状态监控信号连接已跳过（接口不兼容）";
            }
        });
    }
}

void RobotCarMainWindow::setupStyles()
{
    applyTheme(current_theme_);
}

void RobotCarMainWindow::setupWindowGeometry()
{
    // 获取屏幕信息
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    
    // 设置窗口大小为屏幕的80%
    int width = static_cast<int>(screenGeometry.width() * 0.8);
    int height = static_cast<int>(screenGeometry.height() * 0.8);
    
    resize(width, height);
    
    // 居中显示
    move((screenGeometry.width() - width) / 2, (screenGeometry.height() - height) / 2);
    
    default_window_size_ = size();
    default_window_pos_ = pos();
}

// 槽函数实现
void RobotCarMainWindow::onNavigationStarted()
{
    navigation_active_ = true;
    updateButtonStates();
    logMessage("INFO", "导航已开始");
}

void RobotCarMainWindow::onNavigationStopped()
{
    navigation_active_ = false;
    updateButtonStates();
    logMessage("INFO", "导航已停止");
}

void RobotCarMainWindow::onNavigationCompleted()
{
    navigation_active_ = false;
    updateButtonStates();
    logMessage("INFO", "导航已完成");
}

void RobotCarMainWindow::onNavigationFailed(const QString &error)
{
    navigation_active_ = false;
    updateButtonStates();
    logMessage("ERROR", QString("导航失败: %1").arg(error));
}

void RobotCarMainWindow::onEmergencyStop()
{
    if (navigation_active_) {
        navigation_active_ = false;
        updateButtonStates();
    }
    logMessage("WARN", "紧急停止已触发");
}

void RobotCarMainWindow::onGoalPointSelected(double x, double y, double yaw)
{
    logMessage("INFO", QString("目标点已选择: (%.2f, %.2f, %.2f°)").arg(x).arg(y).arg(yaw * 180.0 / M_PI));
}

void RobotCarMainWindow::onWaypointAdded(double x, double y)
{
    logMessage("INFO", QString("路径点已添加: (%.2f, %.2f)").arg(x).arg(y));
}

void RobotCarMainWindow::onPathPointsChanged(const QList<PathPoint> &points)
{
    logMessage("INFO", QString("路径点已更新，共 %1 个点").arg(points.size()));
}

void RobotCarMainWindow::onMarkerAdded(const MapMarker &marker)
{
    logMessage("INFO", QString("标记点已添加: %1").arg(marker.name));
}

void RobotCarMainWindow::onMarkerRemoved(const QString &id)
{
    logMessage("INFO", QString("标记点已删除: %1").arg(id));
}

void RobotCarMainWindow::onMarkerUpdated(const QString &id, const MapMarker &marker)
{
    Q_UNUSED(id)
    logMessage("INFO", QString("标记点已更新: %1").arg(marker.name));
}

void RobotCarMainWindow::onNavigateToMarker(const QString &id)
{
    logMessage("INFO", QString("导航到标记点: %1").arg(id));
}

void RobotCarMainWindow::onSystemStatusChanged(const QString &status)
{
    logMessage("INFO", QString("系统状态: %1").arg(status));
}

void RobotCarMainWindow::onErrorOccurred(const QString &error)
{
    logMessage("ERROR", error);
    showErrorMessage("系统错误", error);
}

void RobotCarMainWindow::onConnectionStatusChanged(bool connected)
{
    connection_established_ = connected;
    connection_status_label_->setText(connected ? "连接状态: 已连接" : "连接状态: 未连接");

    if (connected) {
        connection_status_label_->setStyleSheet("color: green;");
    } else {
        connection_status_label_->setStyleSheet("color: red;");
    }
}

void RobotCarMainWindow::onSaveProject()
{
    saveProject();
}

void RobotCarMainWindow::onLoadProject()
{
    loadProject("");
}

void RobotCarMainWindow::onExportData()
{
    exportProjectData();
}

void RobotCarMainWindow::onImportData()
{
    importProjectData();
}

void RobotCarMainWindow::onSaveSettings()
{
    logMessage("INFO", "设置已保存");
}

void RobotCarMainWindow::onLoadSettings()
{
    logMessage("INFO", "设置已加载");
}

void RobotCarMainWindow::onTabChanged(int index)
{
    Q_UNUSED(index)
    // 根据需要处理标签页切换
}

void RobotCarMainWindow::onFullscreenToggle()
{
    if (fullscreen_mode_) {
        showNormal();
        fullscreen_mode_ = false;
        fullscreen_action_->setText("全屏");
    } else {
        showFullScreen();
        fullscreen_mode_ = true;
        fullscreen_action_->setText("退出全屏");
    }
}

void RobotCarMainWindow::onResetLayout()
{
    // 重置分割器比例
    main_splitter_->setSizes({2, 1});
    logMessage("INFO", "布局已重置");
}

void RobotCarMainWindow::onShowAbout()
{
    QMessageBox::about(this, "关于 RobotCar GUI",
        "RobotCar 导航控制系统\n\n"
        "版本: 2.0\n"
        "基于 ROS2 和 Qt5\n\n"
        "功能特性:\n"
        "• 增强的地图可视化\n"
        "• 多路径点导航控制\n"
        "• 标记点管理系统\n"
        "• 实时状态监控\n"
        "• 触摸屏友好界面");
}

void RobotCarMainWindow::onShowSettings()
{
    main_tabs_->setCurrentWidget(settings_tab_);
}

// 定时更新函数
void RobotCarMainWindow::updateSystemStatus()
{
    // 更新系统状态显示
    if (status_monitor_widget_) {
        // TODO: 从ROS获取实际状态数据
    }
}

void RobotCarMainWindow::updateConnectionStatus()
{
    // 检查ROS连接状态
    if (ros_interface_) {
        bool connected = ros_interface_->isConnected();
        if (connected != connection_established_) {
            onConnectionStatusChanged(connected);
        }
    }
}

// 辅助函数实现
void RobotCarMainWindow::logMessage(const QString &level, const QString &message)
{
    if (status_monitor_widget_) {
        status_monitor_widget_->addLogMessage(level, message);
    }

    // 同时输出到控制台
    qDebug() << QString("[%1] %2").arg(level, message);
}

void RobotCarMainWindow::showErrorMessage(const QString &title, const QString &message)
{
    QMessageBox::critical(this, title, message);
}

void RobotCarMainWindow::showInfoMessage(const QString &title, const QString &message)
{
    QMessageBox::information(this, title, message);
}

void RobotCarMainWindow::showWarningMessage(const QString &title, const QString &message)
{
    QMessageBox::warning(this, title, message);
}

bool RobotCarMainWindow::confirmAction(const QString &title, const QString &message)
{
    return QMessageBox::question(this, title, message) == QMessageBox::Yes;
}

void RobotCarMainWindow::updateButtonStates()
{
    // 更新按钮状态
    if (start_nav_action_) {
        start_nav_action_->setEnabled(!navigation_active_);
    }
    if (stop_nav_action_) {
        stop_nav_action_->setEnabled(navigation_active_);
    }
}

void RobotCarMainWindow::updateStatusDisplay()
{
    // 更新状态显示
}

void RobotCarMainWindow::saveWindowGeometry()
{
    // 保存窗口几何信息
}

void RobotCarMainWindow::restoreWindowGeometry()
{
    // 恢复窗口几何信息
}

void RobotCarMainWindow::applyTheme(const QString &theme_name)
{
    current_theme_ = theme_name;

    // {{ AURA-X: Fix - 修复样式冲突，确保菜单栏和工具栏正确显示. Approval: 用户确认. }}
    if (theme_name == "深色") {
        setStyleSheet(
            "QMainWindow { background-color: #2b2b2b; color: #ffffff; }"
            "QMenuBar { background-color: #2a2a2a; border-bottom: 1px solid #555555; color: #ffffff; }"
            "QMenuBar::item { background-color: transparent; padding: 4px 8px; }"
            "QMenuBar::item:selected { background-color: #404040; }"
            "QToolBar { background-color: #2a2a2a; border: 1px solid #555555; spacing: 3px; }"
            "QTabWidget::pane { border: 1px solid #555555; }"
            "QTabBar::tab { background-color: #3c3c3c; color: #ffffff; padding: 8px; }"
            "QTabBar::tab:selected { background-color: #555555; }"
            // {{ AURA-X: Fix - 添加状态栏样式配置，提高可读性. Approval: 寸止(ID:1735659900). }}
            "QStatusBar { background-color: #2a2a2a; border-top: 1px solid #555555; color: #ffffff; }"
            "QStatusBar QLabel { color: #ffffff; background-color: transparent; padding: 2px 5px; }"
        );
    } else if (theme_name == "浅色") {
        setStyleSheet(
            "QMainWindow { background-color: #f0f0f0; color: #000000; }"
            "QMenuBar { background-color: #f8f8f8; border-bottom: 1px solid #cccccc; color: #000000; }"
            "QMenuBar::item { background-color: transparent; padding: 4px 8px; }"
            "QMenuBar::item:selected { background-color: #e0e0e0; }"
            "QToolBar { background-color: #f8f8f8; border: 1px solid #cccccc; spacing: 3px; }"
            "QTabWidget::pane { border: 1px solid #cccccc; }"
            "QTabBar::tab { background-color: #e0e0e0; color: #000000; padding: 8px; }"
            "QTabBar::tab:selected { background-color: #ffffff; }"
            // {{ AURA-X: Fix - 添加浅色主题状态栏样式配置. Approval: 寸止(ID:1735659900). }}
            "QStatusBar { background-color: #f8f8f8; border-top: 1px solid #cccccc; color: #000000; }"
            "QStatusBar QLabel { color: #000000; background-color: transparent; padding: 2px 5px; }"
        );
    } else {
        // 默认主题，确保菜单栏和工具栏有明确的样式
        setStyleSheet(
            "QMenuBar { background-color: #f0f0f0; border-bottom: 1px solid #d0d0d0; }"
            "QToolBar { background-color: #f0f0f0; border: 1px solid #d0d0d0; spacing: 3px; }"
            // {{ AURA-X: Fix - 添加默认主题状态栏样式配置. Approval: 寸止(ID:1735659900). }}
            "QStatusBar { background-color: #f0f0f0; border-top: 1px solid #d0d0d0; color: #000000; }"
            "QStatusBar QLabel { color: #000000; background-color: transparent; padding: 2px 5px; }"
        );
    }
}

// 项目管理函数
void RobotCarMainWindow::newProject()
{
    current_project_name_ = DEFAULT_PROJECT_NAME;
    current_project_file_.clear();
    logMessage("INFO", "新项目已创建");
}

bool RobotCarMainWindow::saveProject(const QString &filename)
{
    Q_UNUSED(filename)
    logMessage("INFO", "项目保存功能待实现");
    return true;
}

bool RobotCarMainWindow::loadProject(const QString &filename)
{
    Q_UNUSED(filename)
    logMessage("INFO", "项目加载功能待实现");
    return true;
}

void RobotCarMainWindow::setProjectModified(bool modified)
{
    Q_UNUSED(modified)
    // 设置项目修改状态
}

bool RobotCarMainWindow::isProjectModified() const
{
    return false;
}

void RobotCarMainWindow::exportProjectData()
{
    logMessage("INFO", "数据导出功能待实现");
}

void RobotCarMainWindow::importProjectData()
{
    logMessage("INFO", "数据导入功能待实现");
}

// 事件处理函数
void RobotCarMainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    // 处理窗口大小变化
}

void RobotCarMainWindow::closeEvent(QCloseEvent *event)
{
    // 检查是否有未保存的更改
    if (isProjectModified()) {
        if (!confirmAction("退出确认", "项目有未保存的更改，确定要退出吗？")) {
            event->ignore();
            return;
        }
    }

    // 保存窗口几何信息
    saveWindowGeometry();

    logMessage("INFO", "应用程序正在退出");
    event->accept();
}

} // namespace robotcar_gui
