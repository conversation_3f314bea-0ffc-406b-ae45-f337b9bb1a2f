#include "robotcar_gui/ros_interface.hpp"

#include <QDebug>
#include <QStandardPaths>
#include <QDir>

namespace robotcar_gui {

RosInterface::RosInterface(QObject *parent, const QString &namespace_prefix)
    : QObject(parent)
    , rclcpp::Node("robotcar_gui_node")
    , ros_timer_(nullptr)
    , launch_check_timer_(nullptr)
    , connection_check_timer_(nullptr)
    , ros_thread_(nullptr)
    , launch_process_(nullptr)
    , navigation_active_(false)
    , path_following_active_(false)
    , ros_connected_(false)
    , topics_available_(false)
    , namespace_(namespace_prefix.isEmpty() ? "" : namespace_prefix)  // 确保空命名空间
{
    try {
        // 初始化TF2 (commented out for now)
        // tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
        // tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);

        // 延迟初始化订阅者，允许在没有话题的情况下启动
        // 使用QTimer::singleShot来延迟初始化，确保节点完全构造完成
        QTimer::singleShot(100, this, [this]() {
            initializeSubscribers();
        });

        // 创建定时器
        ros_timer_ = new QTimer(this);
        connect(ros_timer_, &QTimer::timeout, this, &RosInterface::spinRos);

        launch_check_timer_ = new QTimer(this);
        connect(launch_check_timer_, &QTimer::timeout, this, &RosInterface::checkLaunchProcess);
        launch_check_timer_->start(1000);  // 每秒检查一次

        connection_check_timer_ = new QTimer(this);
        connect(connection_check_timer_, &QTimer::timeout, this, &RosInterface::checkConnectionStatus);
        connection_check_timer_->start(2000);  // 每2秒检查一次连接状态

        RCLCPP_INFO(this->get_logger(), "RobotCar GUI ROS接口已初始化，命名空间: %s", namespace_.toStdString().c_str());

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "ROS接口初始化失败: %s", e.what());
        // 不抛出异常，让GUI能够继续运行
    }
}

RosInterface::~RosInterface()
{
    stopRosNode();
    stopLaunchFile();
}

void RosInterface::startRosNode()
{
    if (!ros_timer_->isActive()) {
        ros_timer_->start(50);  // 20Hz
        emit systemStatusChanged("ROS节点已启动");
        RCLCPP_INFO(this->get_logger(), "ROS节点已启动");
    }
}

void RosInterface::stopRosNode()
{
    if (ros_timer_->isActive()) {
        ros_timer_->stop();
        emit systemStatusChanged("ROS节点已停止");
        RCLCPP_INFO(this->get_logger(), "ROS节点已停止");
    }
}

void RosInterface::startLaunchFile(const QString &launch_package, const QString &launch_file,
                                  const QStringList &arguments)
{
    // 停止当前运行的launch文件
    stopLaunchFile();

    // 创建新的进程
    launch_process_ = new QProcess(this);

    // 设置环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    env.insert("ROS_DOMAIN_ID", "0");
    launch_process_->setProcessEnvironment(env);

    // 构建命令
    QString program = "ros2";
    QStringList args;
    args << "launch" << launch_package << launch_file;

    // 自动添加命名空间参数（如果没有显式提供）
    bool has_namespace = false;
    for (const QString &arg : arguments) {
        if (arg.startsWith("namespace:=")) {
            has_namespace = true;
            break;
        }
    }

    if (!has_namespace && !namespace_.isEmpty()) {
        args << QString("namespace:=%1").arg(namespace_);
    }

    // 添加其他参数
    args.append(arguments);

    // 连接信号
    connect(launch_process_, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this](int exitCode, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(exitStatus)
                if (exitCode == 0) {
                    emit systemStatusChanged("Launch文件正常退出");
                } else {
                    emit errorOccurred(QString("Launch文件异常退出，退出码: %1").arg(exitCode));
                }
            });

    connect(launch_process_, &QProcess::errorOccurred,
            [this](QProcess::ProcessError error) {
                QString errorMsg;
                switch (error) {
                    case QProcess::FailedToStart:
                        errorMsg = "Launch文件启动失败";
                        break;
                    case QProcess::Crashed:
                        errorMsg = "Launch文件崩溃";
                        break;
                    default:
                        errorMsg = "Launch文件发生未知错误";
                        break;
                }
                emit errorOccurred(errorMsg);
            });

    // 启动进程
    launch_process_->start(program, args);

    if (launch_process_->waitForStarted(5000)) {
        current_launch_package_ = launch_package;
        current_launch_file_ = launch_file;
        emit systemStatusChanged(QString("已启动: %1/%2 (命名空间: %3)").arg(launch_package, launch_file, namespace_));
        RCLCPP_INFO(this->get_logger(), "启动Launch文件: %s/%s，命名空间: %s",
                   launch_package.toStdString().c_str(), launch_file.toStdString().c_str(),
                   namespace_.toStdString().c_str());
    } else {
        emit errorOccurred("Launch文件启动超时");
        delete launch_process_;
        launch_process_ = nullptr;
    }
}

void RosInterface::stopLaunchFile()
{
    if (launch_process_ && launch_process_->state() == QProcess::Running) {
        launch_process_->terminate();
        
        if (!launch_process_->waitForFinished(5000)) {
            launch_process_->kill();
            launch_process_->waitForFinished(2000);
        }
        
        delete launch_process_;
        launch_process_ = nullptr;
        
        current_launch_package_.clear();
        current_launch_file_.clear();
        
        emit systemStatusChanged("Launch文件已停止");
        RCLCPP_INFO(this->get_logger(), "Launch文件已停止");
    }
}

void RosInterface::sendNavigationGoal(double x, double y, double yaw)
{
    if (!nav_action_client_->wait_for_action_server(std::chrono::seconds(5))) {
        emit errorOccurred("导航动作服务器不可用");
        return;
    }
    
    auto goal_msg = nav2_msgs::action::NavigateToPose::Goal();
    goal_msg.pose.header.frame_id = "map";
    goal_msg.pose.header.stamp = this->get_clock()->now();
    goal_msg.pose.pose.position.x = x;
    goal_msg.pose.pose.position.y = y;
    goal_msg.pose.pose.position.z = 0.0;
    
    // 转换yaw角度为四元数
    goal_msg.pose.pose.orientation.z = sin(yaw / 2.0);
    goal_msg.pose.pose.orientation.w = cos(yaw / 2.0);
    
    // 简化版本：直接发送目标，不设置复杂的回调
    nav_action_client_->async_send_goal(goal_msg);
    
    navigation_active_ = true;
    emit navigationStatusChanged(true);
    
    RCLCPP_INFO(this->get_logger(), "发送导航目标: (%.2f, %.2f, %.2f)", x, y, yaw);
}

void RosInterface::sendMultipleWaypoints(const std::vector<geometry_msgs::msg::PoseStamped> &waypoints)
{
    if (waypoints.empty()) {
        emit errorOccurred("路径点列表为空");
        return;
    }

    // 检查follow_waypoints动作服务器是否可用
    if (!waypoints_action_client_) {
        // 如果没有waypoints客户端，使用单点导航逐个执行
        RCLCPP_WARN(this->get_logger(), "多路径点动作服务器不可用，使用单点导航模式");

        // 发送第一个路径点
        if (!waypoints.empty()) {
            const auto& first_waypoint = waypoints[0];
            double yaw = 2.0 * atan2(first_waypoint.pose.orientation.z, first_waypoint.pose.orientation.w);
            sendNavigationGoal(first_waypoint.pose.position.x, first_waypoint.pose.position.y, yaw);
        }
        return;
    }

    if (!waypoints_action_client_->wait_for_action_server(std::chrono::seconds(5))) {
        emit errorOccurred("多路径点导航动作服务器不可用");
        return;
    }

    auto goal_msg = nav2_msgs::action::FollowWaypoints::Goal();
    goal_msg.poses = waypoints;

    // 发送多路径点目标
    waypoints_action_client_->async_send_goal(goal_msg);

    navigation_active_ = true;
    emit navigationStatusChanged(true);

    RCLCPP_INFO(this->get_logger(), "发送多路径点导航目标，共 %zu 个路径点", waypoints.size());
}

void RosInterface::cancelNavigation()
{
    if (navigation_active_) {
        nav_action_client_->async_cancel_all_goals();
        navigation_active_ = false;
        emit navigationStatusChanged(false);
        RCLCPP_INFO(this->get_logger(), "取消导航目标");
    }
}

void RosInterface::emergencyStop()
{
    // 发布紧急停止信号
    auto msg = std_msgs::msg::Bool();
    msg.data = true;
    emergency_stop_pub_->publish(msg);

    // {{ AURA-X: Restore - 恢复零速度命令发送. Approval: 用户确认. }}
    // 发送零速度命令
    geometry_msgs::msg::Twist stop_cmd;
    stop_cmd.linear.x = 0.0;
    stop_cmd.linear.y = 0.0;
    stop_cmd.linear.z = 0.0;
    stop_cmd.angular.x = 0.0;
    stop_cmd.angular.y = 0.0;
    stop_cmd.angular.z = 0.0;
    cmd_vel_pub_->publish(stop_cmd);

    // 取消所有导航目标
    cancelNavigation();

    emit systemStatusChanged("紧急停止已激活");
    RCLCPP_WARN(this->get_logger(), "紧急停止已激活");
}

// {{ AURA-X: Restore - 恢复速度命令发送函数. Approval: 用户确认. }}
void RosInterface::sendVelocityCommand(const geometry_msgs::msg::Twist &cmd)
{
    // {{ AURA-X: Add - 实现速度命令发送功能. Approval: 用户确认. }}
    if (cmd_vel_pub_) {
        cmd_vel_pub_->publish(cmd);
        // RCLCPP_DEBUG(this->get_logger(), "发送速度命令: linear.x=%.2f, angular.z=%.2f",
        //              cmd.linear.x, cmd.angular.z);
    }
}

void RosInterface::startPathFollowing(const QString &path_file, bool loop_mode)
{
    Q_UNUSED(path_file)
    Q_UNUSED(loop_mode)
    
    // 这里应该实现路径跟随逻辑
    // 由于fusion_07已经简化，这里主要是状态管理
    path_following_active_ = true;
    emit pathFollowingStatusChanged(true);
    emit systemStatusChanged("路径跟随已启动");
    RCLCPP_INFO(this->get_logger(), "路径跟随已启动");
}

void RosInterface::stopPathFollowing()
{
    if (path_following_active_) {
        path_following_active_ = false;
        emit pathFollowingStatusChanged(false);
        emit systemStatusChanged("路径跟随已停止");
        RCLCPP_INFO(this->get_logger(), "路径跟随已停止");
    }
}

void RosInterface::spinRos()
{
    try {
        // 确保节点仍然有效且ROS系统正常运行
        if (rclcpp::ok() && this->get_node_base_interface()) {
            // 使用静态执行器来避免shared_from_this问题
            static rclcpp::executors::SingleThreadedExecutor executor;
            static bool node_added = false;

            // 只在第一次调用时添加节点
            if (!node_added) {
                try {
                    executor.add_node(this->get_node_base_interface());
                    node_added = true;
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "添加节点到执行器失败: %s", e.what());
                    return;
                }
            }

            // 执行一次spin操作
            executor.spin_some(std::chrono::milliseconds(10));
        }
    } catch (const std::bad_weak_ptr& e) {
        RCLCPP_WARN(this->get_logger(), "ROS spin weak_ptr异常: %s", e.what());
        // 不发射错误信号，避免GUI显示过多错误信息
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "ROS spin异常: %s", e.what());
        emit errorOccurred(QString("ROS通信异常: %1").arg(e.what()));
    }
}

void RosInterface::checkLaunchProcess()
{
    if (launch_process_ && launch_process_->state() != QProcess::Running) {
        // Launch进程已结束
        if (!current_launch_package_.isEmpty()) {
            emit systemStatusChanged("Launch文件已结束");
            current_launch_package_.clear();
            current_launch_file_.clear();
        }
    }
}

// 回调函数实现
void RosInterface::mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
{
    RCLCPP_INFO(this->get_logger(), "收到地图数据: %dx%d, 分辨率: %.3f",
                msg->info.width, msg->info.height, msg->info.resolution);
    emit mapUpdated(msg);
}

void RosInterface::poseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
{
    emit robotPoseUpdated(msg);
}

void RosInterface::scanCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg)
{
    emit laserScanUpdated(msg);
}

void RosInterface::carInfoCallback(const robotcar_base::msg::CarInfo::SharedPtr msg)
{
    emit carInfoUpdated(msg);
}

void RosInterface::pathCallback(const nav_msgs::msg::Path::SharedPtr msg)
{
    emit pathUpdated(msg);
}

void RosInterface::pathFollowingStatusCallback(const std_msgs::msg::Bool::SharedPtr msg)
{
    path_following_active_ = msg->data;
    emit pathFollowingStatusChanged(msg->data);
}

void RosInterface::trackedPoseCallback(const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg)
{
    // {{ AURA-X: Add - 实现tracked_pose回调函数. Approval: 用户确认. }}
    emit trackedPoseUpdated(msg);
}

// 简化版本：移除复杂的回调函数实现
// 这些功能可以在后续版本中重新实现

void RosInterface::setNamespace(const QString &namespace_prefix)
{
    namespace_ = namespace_prefix;
    // 重新初始化订阅者和发布者
    initializeSubscribers();
    RCLCPP_INFO(this->get_logger(), "命名空间已更新为: %s", namespace_.toStdString().c_str());
}

QString RosInterface::addNamespace(const QString &topic_name) const
{
    // 如果命名空间为空，直接返回话题名称（确保以/开头）
    if (namespace_.isEmpty()) {
        if (topic_name.startsWith("/")) {
            return topic_name;
        } else {
            return QString("/%1").arg(topic_name);
        }
    }

    // 如果有命名空间，按原逻辑处理
    if (topic_name.startsWith("/")) {
        return QString("/%1%2").arg(namespace_, topic_name);
    }
    return QString("/%1/%2").arg(namespace_, topic_name);
}

void RosInterface::initializeSubscribers()
{
    try {
        // 确保节点已完全初始化
        if (!this->get_node_base_interface()) {
            RCLCPP_WARN(this->get_logger(), "节点接口未就绪，延迟初始化订阅者");
            QTimer::singleShot(500, this, &RosInterface::initializeSubscribers);
            return;
        }

        // 创建订阅者（带命名空间）
        QString map_topic = addNamespace("/map");
        RCLCPP_INFO(this->get_logger(), "订阅地图话题: %s", map_topic.toStdString().c_str());
        map_sub_ = this->create_subscription<nav_msgs::msg::OccupancyGrid>(
            map_topic.toStdString(), 10,
            std::bind(&RosInterface::mapCallback, this, std::placeholders::_1));

        // {{ AURA-X: Fix - 添加激光雷达订阅者，使用融合后的话题. Approval: 寸止(ID:1735659800). }}
        QString scan_topic = addNamespace("/merged");  // 使用融合后的激光雷达数据
        RCLCPP_INFO(this->get_logger(), "订阅激光雷达话题: %s", scan_topic.toStdString().c_str());
        scan_sub_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
            scan_topic.toStdString(), 10,
            std::bind(&RosInterface::scanCallback, this, std::placeholders::_1));

        // {{ AURA-X: Fix - 添加机器人位置订阅者（两种消息类型）. Approval: 寸止(ID:1735659900). }}
        QString pose_topic = addNamespace("/tracked_pose");
        RCLCPP_INFO(this->get_logger(), "订阅机器人位置话题: %s", pose_topic.toStdString().c_str());

        // 订阅 PoseStamped 消息类型
        pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            pose_topic.toStdString(), 10,
            std::bind(&RosInterface::poseCallback, this, std::placeholders::_1));

        // 订阅 PoseWithCovarianceStamped 消息类型
        tracked_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
            pose_topic.toStdString(), 10,
            std::bind(&RosInterface::trackedPoseCallback, this, std::placeholders::_1));

        // {{ AURA-X: Fix - 添加车辆信息订阅者. Approval: 寸止(ID:1735659800). }}
        QString car_info_topic = addNamespace("/car_info");
        RCLCPP_INFO(this->get_logger(), "订阅车辆信息话题: %s", car_info_topic.toStdString().c_str());
        car_info_sub_ = this->create_subscription<robotcar_base::msg::CarInfo>(
            car_info_topic.toStdString(), 10,
            std::bind(&RosInterface::carInfoCallback, this, std::placeholders::_1));

        path_sub_ = this->create_subscription<nav_msgs::msg::Path>(
            addNamespace("/plan").toStdString(), 10,
            std::bind(&RosInterface::pathCallback, this, std::placeholders::_1));

        path_following_status_sub_ = this->create_subscription<std_msgs::msg::Bool>(
            addNamespace("/path_following_status").toStdString(), 10,
            std::bind(&RosInterface::pathFollowingStatusCallback, this, std::placeholders::_1));

        // 创建发布者（带命名空间）
        goal_pub_ = this->create_publisher<geometry_msgs::msg::PoseStamped>(
            addNamespace("/goal_pose").toStdString(), 10);
        emergency_stop_pub_ = this->create_publisher<std_msgs::msg::Bool>(
            addNamespace("/emergency_stop").toStdString(), 10);
        // {{ AURA-X: Fix - 修改为diff_drive_controller速度命令话题. Approval: 用户确认. }}
        cmd_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>(
            addNamespace("/diff_drive_controller/cmd_vel_unstamped").toStdString(), 10);

        // 创建动作客户端（带命名空间）- 使用延迟初始化避免weak_ptr问题
        try {
            nav_action_client_ = rclcpp_action::create_client<nav2_msgs::action::NavigateToPose>(
                this, addNamespace("/navigate_to_pose").toStdString());
        } catch (const std::exception& e) {
            RCLCPP_WARN(this->get_logger(), "创建导航动作客户端失败: %s", e.what());
        }

        try {
            waypoints_action_client_ = rclcpp_action::create_client<nav2_msgs::action::FollowWaypoints>(
                this, addNamespace("/follow_waypoints").toStdString());
        } catch (const std::exception& e) {
            RCLCPP_WARN(this->get_logger(), "创建路径跟随动作客户端失败: %s", e.what());
        }

        RCLCPP_INFO(this->get_logger(), "订阅者和发布者已初始化，命名空间: %s", namespace_.toStdString().c_str());

    } catch (const std::bad_weak_ptr& e) {
        RCLCPP_WARN(this->get_logger(), "初始化ROS接口时出现weak_ptr错误: %s", e.what());
        // 重试初始化
        QTimer::singleShot(1000, this, &RosInterface::initializeSubscribers);
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "初始化ROS接口时出错: %s", e.what());
        emit errorOccurred(QString("ROS接口初始化失败: %1").arg(e.what()));
    }
}

void RosInterface::checkConnectionStatus()
{
    try {
        // 检查ROS2连接状态
        if (!rclcpp::ok()) {
            if (ros_connected_) {
                ros_connected_ = false;
                emit connectionStatusChanged(false);
                emit systemStatusChanged("ROS2系统已关闭");
            }
            return;
        }

        auto node_names = this->get_node_names();
        bool was_connected = ros_connected_;
        ros_connected_ = !node_names.empty();

        if (was_connected != ros_connected_) {
            emit connectionStatusChanged(ros_connected_);
            if (ros_connected_) {
                emit systemStatusChanged("ROS2连接已建立");
            } else {
                emit systemStatusChanged("ROS2连接已断开");
            }
        }

        // 检查话题可用性（但不要求所有话题都存在）
        checkTopicsAvailability();

    } catch (const std::bad_weak_ptr& e) {
        RCLCPP_WARN(this->get_logger(), "连接状态检查时出现weak_ptr错误: %s", e.what());
        // 不改变连接状态，避免误报
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "连接状态检查失败: %s", e.what());
        if (ros_connected_) {
            ros_connected_ = false;
            emit connectionStatusChanged(false);
            emit systemStatusChanged("ROS2连接检查失败");
        }
    }
}

void RosInterface::checkTopicsAvailability()
{
    try {
        auto topic_names = this->get_topic_names_and_types();

        // 检查核心话题是否存在（更宽松的检查）
        QStringList core_topics = {
            addNamespace("/scan"),  // 激光扫描数据 - 最重要
        };

        // 可选话题（在不同模式下可能不存在）
        QStringList optional_topics = {
            addNamespace("/map"),           // 地图数据 - 仅在mapping模式下可用
            addNamespace("/tracked_pose"),  // 位置数据 - 仅在有定位时可用
            addNamespace("/car_info"),      // 车辆信息 - 可选
            addNamespace("/plan")           // 规划路径 - 仅在navigation模式下可用
        };

        // 检查核心话题
        int available_core = 0;
        for (const QString &topic : core_topics) {
            for (const auto &topic_info : topic_names) {
                if (QString::fromStdString(topic_info.first) == topic) {
                    available_core++;
                    break;
                }
            }
        }

        // 检查可选话题
        int available_optional = 0;
        for (const QString &topic : optional_topics) {
            for (const auto &topic_info : topic_names) {
                if (QString::fromStdString(topic_info.first) == topic) {
                    available_optional++;
                    break;
                }
            }
        }

        // 只要有核心话题就认为系统可用
        bool system_available = (available_core > 0);

        if (topics_available_ != system_available) {
            topics_available_ = system_available;
            emit topicsAvailabilityChanged(topics_available_);

            if (topics_available_) {
                emit systemStatusChanged(QString("ROS系统已连接 (核心话题: %1/%2, 可选话题: %3/%4)")
                                       .arg(available_core).arg(core_topics.size())
                                       .arg(available_optional).arg(optional_topics.size()));
            } else {
                emit systemStatusChanged("等待ROS话题连接...");
            }
        }

    } catch (const std::bad_weak_ptr& e) {
        RCLCPP_WARN(this->get_logger(), "检查话题可用性时出现weak_ptr错误: %s", e.what());
    } catch (const std::exception& e) {
        RCLCPP_WARN(this->get_logger(), "检查话题可用性时出错: %s", e.what());
    }
}

bool RosInterface::areTopicsAvailable() const
{
    return topics_available_;
}

} // namespace robotcar_gui
