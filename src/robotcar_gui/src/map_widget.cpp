#include "robotcar_gui/map_widget.hpp"

#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QContextMenuEvent>
#include <QTimer>  // {{ AURA-X: Add - 添加QTimer头文件. Approval: 用户确认. }}
#include <QDebug>
#include <cmath>
#include <algorithm>

namespace robotcar_gui {

MapWidget::MapWidget(QWidget *parent)
    : QWidget(parent)
    , map_updated_(false)
    , show_robot_(true)
    , show_path_(true)
    , show_laser_scan_(true)  // {{ AURA-X: Fix - 默认显示激光雷达数据. Approval: 用户确认. }}
    , show_grid_(true)
    , show_markers_(true)
    , show_robot_footprint_(true)
    , scale_(3.0)  // {{ AURA-X: Modify - 将初始缩放比例从1.0调整为3.0. Approval: 用户确认. }}
    , min_scale_(0.05)
    , max_scale_(50.0)  // 增加最大缩放比例，让用户可以放大更多
    , dragging_(false)
    , interaction_mode_(NONE)
    , context_menu_(nullptr)
    , path_execution_count_(1)
    , ros_interface_(nullptr)
{
    setMinimumSize(800, 600);  // 增加地图的最小尺寸以适应新功能
    setMouseTracking(true);

    // 设置背景色
    setStyleSheet("background-color: #1a1a1a;");

    // 创建右键菜单
    setupContextMenu();

    // 初始化变换
    updateTransform();
}

MapWidget::~MapWidget()
{
}

void MapWidget::setRosInterface(RosInterface *ros_interface)
{
    ros_interface_ = ros_interface;
}

void MapWidget::setupContextMenu()
{
    context_menu_ = new QMenu(this);

    set_goal_action_ = context_menu_->addAction("设置目标点", this, &MapWidget::onSetGoalHere);
    add_waypoint_action_ = context_menu_->addAction("添加路径点", this, &MapWidget::onAddWaypointHere);
    add_marker_action_ = context_menu_->addAction("添加标记点", this, &MapWidget::onAddMarkerHere);
    context_menu_->addSeparator();

    clear_path_action_ = context_menu_->addAction("清除路径", this, &MapWidget::onClearPath);
    execute_path_action_ = context_menu_->addAction("执行路径", this, &MapWidget::onExecutePath);
    context_menu_->addSeparator();

    edit_marker_action_ = context_menu_->addAction("编辑标记点", this, &MapWidget::onEditMarker);
    delete_marker_action_ = context_menu_->addAction("删除标记点", this, &MapWidget::onDeleteMarker);
    context_menu_->addSeparator();

    center_robot_action_ = context_menu_->addAction("居中机器人", this, &MapWidget::onCenterOnRobot);
    reset_view_action_ = context_menu_->addAction("重置视图", this, &MapWidget::onResetView);
    context_menu_->addSeparator();

    // {{ AURA-X: Add - 添加激光雷达显示控制. Approval: 用户确认. }}
    show_laser_action_ = context_menu_->addAction("显示激光雷达", this, &MapWidget::onToggleLaserScan);
    show_laser_action_->setCheckable(true);
    show_laser_action_->setChecked(show_laser_scan_);

    // 初始状态下禁用某些动作
    edit_marker_action_->setEnabled(false);
    delete_marker_action_->setEnabled(false);
}

void MapWidget::updateMap(const nav_msgs::msg::OccupancyGrid::SharedPtr map)
{
    qDebug() << "MapWidget: 收到地图数据更新";
    map_data_ = map;
    map_updated_ = true;

    // 创建地图像素图
    if (map_data_) {
        qDebug() << "MapWidget: 地图尺寸:" << map_data_->info.width << "x" << map_data_->info.height
                 << "分辨率:" << map_data_->info.resolution;
        int width = map_data_->info.width;
        int height = map_data_->info.height;
        
        map_pixmap_ = QPixmap(width, height);
        map_pixmap_.fill(Qt::gray);
        
        QPainter painter(&map_pixmap_);
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = y * width + x;
                int8_t value = map_data_->data[index];
                
                QColor color;
                if (value == -1) {
                    color = Qt::gray;  // 未知区域
                } else if (value == 0) {
                    color = Qt::white;  // 自由空间
                } else {
                    color = Qt::black;  // 障碍物
                }
                
                painter.setPen(color);
                painter.drawPoint(x, height - 1 - y);  // 翻转Y轴
            }
        }
    }

    // {{ AURA-X: Fix - 修复地图抖动问题，只在首次加载时调整视图. Approval: 用户确认. }}
    // 只在首次地图加载时自动调整视图，避免每次更新都触发抖动
    static bool first_map_loaded = false;
    if (map_data_ && !first_map_loaded) {
        qDebug() << "MapWidget: 首次地图加载，自动适配视图";
        fitToMap();
        first_map_loaded = true;

        // 如果已有机器人位姿，延迟居中（避免与fitToMap冲突）
        if (robot_pose_) {
            QTimer::singleShot(300, this, &MapWidget::centerOnRobot);
        }
    }

    update();
}

void MapWidget::updateRobotPose(const geometry_msgs::msg::PoseStamped::SharedPtr pose)
{
    // {{ AURA-X: Fix - 修复位姿更新逻辑，避免重复居中导致抖动. Approval: 用户确认. }}
    static bool first_pose_centered = false;
    bool first_pose = (robot_pose_ == nullptr);
    robot_pose_ = pose;

    // {{ AURA-X: Add - 添加机器人位置数据更新调试日志. Approval: 寸止(ID:1735659900). }}
    qDebug() << "机器人位置已更新:" << pose->pose.position.x << pose->pose.position.y;

    // 只在首次接收到机器人位姿时自动居中，且确保不重复执行
    if (first_pose && map_data_ && robot_pose_ && !first_pose_centered) {
        qDebug() << "MapWidget: 首次接收机器人位姿，自动居中显示";
        first_pose_centered = true;
        // 使用较长延迟，确保地图已完全加载
        QTimer::singleShot(500, this, &MapWidget::centerOnRobot);
    }

    update();
}

void MapWidget::updatePath(const nav_msgs::msg::Path::SharedPtr path)
{
    path_data_ = path;
    update();
}

void MapWidget::updateLaserScan(const sensor_msgs::msg::LaserScan::SharedPtr scan)
{
    laser_scan_ = scan;
    // {{ AURA-X: Add - 添加激光雷达数据更新调试日志. Approval: 寸止(ID:1735659900). }}
    qDebug() << "激光雷达数据已更新，点数:" << scan->ranges.size() << "显示状态:" << show_laser_scan_;
    if (show_laser_scan_) {
        update();
    }
}

void MapWidget::resetView()
{
    // {{ AURA-X: Modify - 重置视图时使用3.0缩放比例. Approval: 用户确认. }}
    scale_ = 3.0;
    offset_ = QPointF(0, 0);
    updateTransform();
    update();
}

void MapWidget::centerOnRobot()
{
    if (robot_pose_) {
        double robot_x = robot_pose_->pose.position.x;
        double robot_y = robot_pose_->pose.position.y;
        
        QPointF robot_widget = mapToWidget(robot_x, robot_y);
        QPointF center = QPointF(width() / 2.0, height() / 2.0);
        
        offset_ += center - robot_widget;
        updateTransform();
        update();
    }
}

void MapWidget::fitToMap()
{
    if (!map_data_) return;
    
    double map_width = map_data_->info.width * map_data_->info.resolution;
    double map_height = map_data_->info.height * map_data_->info.resolution;
    
    double scale_x = width() / map_width;
    double scale_y = height() / map_height;
    
    // {{ AURA-X: Modify - 计算合适的缩放比例并确保地图居中显示. Approval: 用户确认. }}
    scale_ = std::min(scale_x, scale_y) * 0.9;  // 留一些边距
    scale_ = std::max(min_scale_, std::min(max_scale_, scale_));

    // 计算地图中心点在世界坐标系中的位置
    double map_center_x = map_data_->info.origin.position.x + map_width / 2.0;
    double map_center_y = map_data_->info.origin.position.y + map_height / 2.0;

    // 设置偏移量使地图中心点显示在控件中心
    // 由于updateTransform()会将坐标原点移到控件中心，我们只需要设置负的地图中心坐标作为偏移
    offset_ = QPointF(-map_center_x * scale_, map_center_y * scale_);  // 注意Y轴翻转
    updateTransform();
    update();
}

void MapWidget::resetAutoAdjustment()
{
    // {{ AURA-X: Add - 重置自动调整状态，允许重新自动居中. Approval: 用户确认. }}
    static bool *first_map_loaded_ptr = nullptr;
    static bool *first_pose_centered_ptr = nullptr;

    // 这是一个简单的重置方法，实际使用中可能需要更复杂的状态管理
    qDebug() << "MapWidget: 重置自动调整状态";

    // 手动触发重新适配
    if (map_data_) {
        fitToMap();
        if (robot_pose_) {
            QTimer::singleShot(300, this, &MapWidget::centerOnRobot);
        }
    }
}

void MapWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 清空背景
    painter.fillRect(rect(), QColor(26, 26, 26));
    
    // 应用变换
    painter.setTransform(transform_);
    
    // 绘制网格
    if (show_grid_) {
        drawGrid(painter);
    }
    
    // 绘制地图
    if (map_data_ && map_updated_) {
        drawMap(painter);
    }
    
    // 绘制路径
    if (show_path_ && path_data_) {
        drawPath(painter);
    }
    
    // 绘制激光扫描
    if (show_laser_scan_ && laser_scan_) {
        drawLaserScan(painter);
    }
    
    // 绘制机器人
    if (show_robot_ && robot_pose_) {
        drawRobot(painter);
    }

    // 绘制机器人轮廓
    if (show_robot_footprint_ && robot_pose_) {
        QPointF robot_pos(robot_pose_->pose.position.x, robot_pose_->pose.position.y);
        double qz = robot_pose_->pose.orientation.z;
        double qw = robot_pose_->pose.orientation.w;
        double yaw = 2.0 * atan2(qz, qw);
        drawRobotFootprint(painter, robot_pos, yaw);
    }

    // 绘制标记点
    if (show_markers_) {
        drawMarkers(painter);
    }

    // 绘制路径点
    drawPathPoints(painter);

    // 绘制坐标系
    drawCoordinateSystem(painter);
}

void MapWidget::drawMap(QPainter &painter)
{
    if (!map_data_ || map_pixmap_.isNull()) return;
    
    double origin_x = map_data_->info.origin.position.x;
    double origin_y = map_data_->info.origin.position.y;
    double resolution = map_data_->info.resolution;
    
    QRectF map_rect(origin_x, origin_y, 
                   map_data_->info.width * resolution,
                   map_data_->info.height * resolution);
    
    painter.drawPixmap(map_rect, map_pixmap_, map_pixmap_.rect());
}

void MapWidget::drawRobot(QPainter &painter)
{
    if (!robot_pose_) return;
    
    double x = robot_pose_->pose.position.x;
    double y = robot_pose_->pose.position.y;

    // 计算机器人朝向
    double qz = robot_pose_->pose.orientation.z;
    double qw = robot_pose_->pose.orientation.w;
    double yaw = 2.0 * atan2(qz, qw);
    
    painter.save();
    painter.translate(x, y);
    painter.rotate(yaw * 180.0 / M_PI);
    
    // 绘制机器人本体（矩形）
    painter.setBrush(QColor(42, 130, 218));
    painter.setPen(QPen(Qt::white, 0.05));
    painter.drawRect(QRectF(-ROBOT_SIZE/2, -ROBOT_SIZE/2, ROBOT_SIZE, ROBOT_SIZE));
    
    // 绘制方向箭头
    painter.setBrush(Qt::red);
    painter.setPen(Qt::NoPen);
    QPolygonF arrow;
    arrow << QPointF(ROBOT_SIZE/2, 0)
          << QPointF(ROBOT_SIZE/2 - ARROW_SIZE, -ARROW_SIZE/2)
          << QPointF(ROBOT_SIZE/2 - ARROW_SIZE, ARROW_SIZE/2);
    painter.drawPolygon(arrow);
    
    painter.restore();
}

void MapWidget::drawPath(QPainter &painter)
{
    if (!path_data_ || path_data_->poses.empty()) return;
    
    painter.setPen(QPen(QColor(255, 165, 0), PATH_WIDTH));  // 橙色路径
    
    for (size_t i = 1; i < path_data_->poses.size(); ++i) {
        double x1 = path_data_->poses[i-1].pose.position.x;
        double y1 = path_data_->poses[i-1].pose.position.y;
        double x2 = path_data_->poses[i].pose.position.x;
        double y2 = path_data_->poses[i].pose.position.y;
        
        painter.drawLine(QPointF(x1, y1), QPointF(x2, y2));
    }
}

void MapWidget::drawLaserScan(QPainter &painter)
{
    if (!laser_scan_ || !robot_pose_) {
        // {{ AURA-X: Add - 添加激光雷达绘制调试日志. Approval: 寸止(ID:1735659900). }}
        if (!laser_scan_) qDebug() << "激光雷达数据为空，无法绘制";
        if (!robot_pose_) qDebug() << "机器人位置数据为空，无法绘制激光雷达";
        return;
    }
    
    double robot_x = robot_pose_->pose.position.x;
    double robot_y = robot_pose_->pose.position.y;
    double robot_yaw = 2.0 * atan2(robot_pose_->pose.orientation.z,
                                  robot_pose_->pose.orientation.w);
    
    painter.setPen(QPen(Qt::red, 0.02));
    
    for (size_t i = 0; i < laser_scan_->ranges.size(); ++i) {
        float range = laser_scan_->ranges[i];
        if (range < laser_scan_->range_min || range > laser_scan_->range_max) continue;
        
        double angle = laser_scan_->angle_min + i * laser_scan_->angle_increment + robot_yaw;
        double end_x = robot_x + range * cos(angle);
        double end_y = robot_y + range * sin(angle);
        
        painter.drawLine(QPointF(robot_x, robot_y), QPointF(end_x, end_y));
    }
}

void MapWidget::drawGrid(QPainter &painter)
{
    painter.setPen(QPen(QColor(64, 64, 64), 0.01));
    
    // 获取可见区域
    QRectF visible_rect = transform_.inverted().mapRect(rect());
    
    // 绘制网格线
    double grid_start_x = floor(visible_rect.left() / GRID_SIZE) * GRID_SIZE;
    double grid_start_y = floor(visible_rect.top() / GRID_SIZE) * GRID_SIZE;
    
    for (double x = grid_start_x; x <= visible_rect.right(); x += GRID_SIZE) {
        painter.drawLine(QPointF(x, visible_rect.top()), QPointF(x, visible_rect.bottom()));
    }
    
    for (double y = grid_start_y; y <= visible_rect.bottom(); y += GRID_SIZE) {
        painter.drawLine(QPointF(visible_rect.left(), y), QPointF(visible_rect.right(), y));
    }
}

void MapWidget::drawCoordinateSystem(QPainter &painter)
{
    painter.save();
    painter.resetTransform();
    
    // 在左下角绘制坐标系
    painter.translate(30, height() - 30);
    
    // X轴（红色）
    painter.setPen(QPen(Qt::red, 2));
    painter.drawLine(0, 0, 20, 0);
    painter.drawText(25, 5, "X");
    
    // Y轴（绿色）
    painter.setPen(QPen(Qt::green, 2));
    painter.drawLine(0, 0, 0, -20);
    painter.drawText(5, -25, "Y");
    
    painter.restore();
}

QPointF MapWidget::mapToWidget(double x, double y) const
{
    return transform_.map(QPointF(x, y));
}

QPointF MapWidget::widgetToMap(const QPointF &point) const
{
    return transform_.inverted().map(point);
}

QPointF MapWidget::widgetToMap(double x, double y) const
{
    return widgetToMap(QPointF(x, y));
}

void MapWidget::updateTransform()
{
    transform_.reset();
    transform_.translate(width() / 2.0 + offset_.x(), height() / 2.0 + offset_.y());
    transform_.scale(scale_, -scale_);  // 翻转Y轴
}

// 绘制标记点
void MapWidget::drawMarkers(QPainter &painter)
{
    painter.save();

    for (const auto &marker : markers_) {
        if (!marker.visible) continue;

        QPointF widget_pos = mapToWidget(marker.position.x(), marker.position.y());

        // 绘制标记点
        painter.setBrush(marker.color);
        painter.setPen(QPen(Qt::white, 2));

        double size = MARKER_SIZE * scale_;
        if (marker.icon_type == "circle") {
            painter.drawEllipse(widget_pos, size, size);
        } else if (marker.icon_type == "square") {
            painter.drawRect(widget_pos.x() - size/2, widget_pos.y() - size/2, size, size);
        } else if (marker.icon_type == "triangle") {
            QPolygonF triangle;
            triangle << QPointF(widget_pos.x(), widget_pos.y() - size/2)
                     << QPointF(widget_pos.x() - size/2, widget_pos.y() + size/2)
                     << QPointF(widget_pos.x() + size/2, widget_pos.y() + size/2);
            painter.drawPolygon(triangle);
        }

        // 绘制标记点名称
        if (scale_ > 0.5) {  // 只在足够大的缩放级别显示文字
            painter.setPen(Qt::white);
            painter.setFont(QFont("Arial", 10));
            painter.drawText(widget_pos + QPointF(size + 5, 5), marker.name);
        }
    }

    painter.restore();
}

// 绘制路径点
void MapWidget::drawPathPoints(QPainter &painter)
{
    painter.save();

    for (int i = 0; i < path_points_.size(); ++i) {
        const auto &point = path_points_[i];
        QPointF widget_pos = mapToWidget(point.position.x(), point.position.y());

        // 绘制路径点
        painter.setBrush(QColor(255, 165, 0, 180));  // 橙色半透明
        painter.setPen(QPen(Qt::white, 2));

        double size = WAYPOINT_SIZE * scale_;
        painter.drawEllipse(widget_pos, size, size);

        // 绘制序号
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 8, QFont::Bold));
        painter.drawText(widget_pos - QPointF(5, -3), QString::number(i + 1));

        // 绘制朝向箭头
        if (point.orientation != 0.0) {
            painter.save();
            painter.translate(widget_pos);
            painter.rotate(point.orientation * 180.0 / M_PI);
            painter.setPen(QPen(Qt::yellow, 2));
            painter.drawLine(0, 0, size, 0);
            painter.drawLine(size, 0, size - 5, -3);
            painter.drawLine(size, 0, size - 5, 3);
            painter.restore();
        }
    }

    // 绘制路径连接线
    if (path_points_.size() > 1) {
        painter.setPen(QPen(QColor(255, 165, 0, 128), 2, Qt::DashLine));
        for (int i = 0; i < path_points_.size() - 1; ++i) {
            QPointF start = mapToWidget(path_points_[i].position.x(), path_points_[i].position.y());
            QPointF end = mapToWidget(path_points_[i + 1].position.x(), path_points_[i + 1].position.y());
            painter.drawLine(start, end);
        }
    }

    painter.restore();
}

// 绘制机器人轮廓
void MapWidget::drawRobotFootprint(QPainter &painter, const QPointF &position, double orientation)
{
    painter.save();
    painter.translate(mapToWidget(position.x(), position.y()));
    painter.rotate(orientation * 180.0 / M_PI);

    // 绘制机器人实际尺寸轮廓 (750mm x 600mm)
    double width_scaled = ROBOT_WIDTH * scale_;
    double length_scaled = ROBOT_LENGTH * scale_;

    painter.setBrush(QColor(42, 130, 218, 50));  // 半透明蓝色
    painter.setPen(QPen(QColor(42, 130, 218), 2));
    painter.drawRect(QRectF(-length_scaled/2, -width_scaled/2, length_scaled, width_scaled));

    // 绘制前进方向指示
    painter.setPen(QPen(Qt::red, 3));
    painter.drawLine(0, 0, length_scaled/2, 0);
    painter.drawLine(length_scaled/2, 0, length_scaled/2 - 10, -5);
    painter.drawLine(length_scaled/2, 0, length_scaled/2 - 10, 5);

    painter.restore();
}

void MapWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        drag_start_pos_ = event->pos();

        // 根据交互模式处理点击
        switch (interaction_mode_) {
            case SET_GOAL: {
                QPointF map_pos = widgetToMap(event->pos());
                handleGoalSetting(map_pos);
                break;
            }
            case ADD_WAYPOINT: {
                QPointF map_pos = widgetToMap(event->pos());
                handleWaypointAdding(map_pos);
                break;
            }
            case ADD_MARKER: {
                QPointF map_pos = widgetToMap(event->pos());
                handleMarkerPlacement(map_pos);
                break;
            }
            default:
                // 检查是否点击了标记点
                MapMarker* marker = findMarkerAt(event->pos());
                if (marker) {
                    selected_marker_id_ = marker->id;
                } else {
                    selected_marker_id_.clear();
                    dragging_ = true;
                    last_mouse_pos_ = event->pos();
                }
                break;
        }
    }
}

void MapWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (dragging_) {
        QPointF delta = event->pos() - last_mouse_pos_;
        offset_ += delta;
        last_mouse_pos_ = event->pos();
        updateTransform();
        update();
    }
}

void MapWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        dragging_ = false;
    }
}

void MapWidget::wheelEvent(QWheelEvent *event)
{
    double factor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
    double new_scale = scale_ * factor;
    
    if (new_scale >= min_scale_ && new_scale <= max_scale_) {
        QPointF mouse_pos = event->position();
        QPointF map_pos = widgetToMap(mouse_pos);
        
        scale_ = new_scale;
        updateTransform();
        
        QPointF new_mouse_pos = mapToWidget(map_pos.x(), map_pos.y());
        offset_ += mouse_pos - new_mouse_pos;
        updateTransform();
        
        update();
    }
}

void MapWidget::contextMenuEvent(QContextMenuEvent *event)
{
    context_menu_pos_ = event->pos();

    // 检查是否右键点击了标记点
    MapMarker* marker = findMarkerAt(event->pos());
    if (marker) {
        selected_marker_id_ = marker->id;
        edit_marker_action_->setEnabled(true);
        delete_marker_action_->setEnabled(true);
        edit_marker_action_->setText(QString("编辑 '%1'").arg(marker->name));
        delete_marker_action_->setText(QString("删除 '%1'").arg(marker->name));
    } else {
        selected_marker_id_.clear();
        edit_marker_action_->setEnabled(false);
        delete_marker_action_->setEnabled(false);
        edit_marker_action_->setText("编辑标记点");
        delete_marker_action_->setText("删除标记点");
    }

    // 根据路径点数量启用/禁用相关动作
    execute_path_action_->setEnabled(!path_points_.isEmpty());
    clear_path_action_->setEnabled(!path_points_.isEmpty());

    context_menu_->exec(event->globalPos());
}

void MapWidget::onSetGoalHere()
{
    double yaw = 0.0;  // 默认朝向
    emit goalPointSelected(context_menu_pos_.x(), context_menu_pos_.y(), yaw);
}

void MapWidget::onAddWaypointHere()
{
    emit waypointAdded(context_menu_pos_.x(), context_menu_pos_.y());
}

void MapWidget::onCenterOnRobot()
{
    centerOnRobot();
}

void MapWidget::onResetView()
{
    resetView();
}

void MapWidget::onToggleLaserScan()
{
    // {{ AURA-X: Add - 切换激光雷达显示状态. Approval: 用户确认. }}
    show_laser_scan_ = !show_laser_scan_;
    show_laser_action_->setChecked(show_laser_scan_);
    qDebug() << "MapWidget: 激光雷达显示" << (show_laser_scan_ ? "开启" : "关闭");
    update();
}

void MapWidget::onAddMarkerHere()
{
    if (!context_menu_pos_.isNull()) {
        QPointF map_pos = widgetToMap(context_menu_pos_);
        handleMarkerPlacement(map_pos);
    }
}

void MapWidget::onClearPath()
{
    clearPathPoints();
    update();
}

void MapWidget::onExecutePath()
{
    if (!path_points_.isEmpty()) {
        emit navigationRequested(path_points_, path_execution_count_);
    }
}

void MapWidget::onEditMarker()
{
    if (!selected_marker_id_.isEmpty()) {
        MapMarker* marker = findMarkerAt(context_menu_pos_);
        if (marker) {
            showMarkerDialog(*marker, false);
        }
    }
}

void MapWidget::onDeleteMarker()
{
    if (!selected_marker_id_.isEmpty()) {
        removeMarker(selected_marker_id_);
        selected_marker_id_.clear();
    }
}

void MapWidget::onSaveMarkers()
{
    QString filename = QFileDialog::getSaveFileName(this,
        "保存标记点",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/markers.json",
        "JSON文件 (*.json)");

    if (!filename.isEmpty()) {
        saveMarkersToFile(filename);
    }
}

void MapWidget::onLoadMarkers()
{
    QString filename = QFileDialog::getOpenFileName(this,
        "加载标记点",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "JSON文件 (*.json)");

    if (!filename.isEmpty()) {
        loadMarkersFromFile(filename);
    }
}

// 标记点管理函数
void MapWidget::addMarker(const MapMarker &marker)
{
    markers_.append(marker);
    emit markerAdded(marker);
    update();
}

void MapWidget::removeMarker(const QString &id)
{
    for (int i = 0; i < markers_.size(); ++i) {
        if (markers_[i].id == id) {
            markers_.removeAt(i);
            emit markerRemoved(id);
            update();
            break;
        }
    }
}

void MapWidget::updateMarker(const QString &id, const MapMarker &marker)
{
    for (int i = 0; i < markers_.size(); ++i) {
        if (markers_[i].id == id) {
            markers_[i] = marker;
            emit markerUpdated(id, marker);
            update();
            break;
        }
    }
}

// 路径点管理函数
void MapWidget::addPathPoint(const PathPoint &point)
{
    path_points_.append(point);
    emit pathPointsChanged(path_points_);
    update();
}

void MapWidget::clearPathPoints()
{
    path_points_.clear();
    emit pathPointsChanged(path_points_);
    update();
}

// 交互处理函数
void MapWidget::handleGoalSetting(const QPointF &map_pos)
{
    geometry_msgs::msg::PoseStamped goal;
    goal.header.frame_id = "map";
    // TODO: 设置时间戳，需要ROS接口
    goal.header.stamp.sec = 0;
    goal.header.stamp.nanosec = 0;
    goal.pose.position.x = map_pos.x();
    goal.pose.position.y = map_pos.y();
    goal.pose.position.z = 0.0;

    // 设置默认朝向
    goal.pose.orientation.w = 1.0;

    emit goalPointSelected(map_pos.x(), map_pos.y(), 0.0);
}

void MapWidget::handleWaypointAdding(const QPointF &map_pos)
{
    PathPoint point;
    point.position = map_pos;
    point.orientation = 0.0;
    point.repeat_count = 1;
    point.is_waypoint = true;

    addPathPoint(point);
    emit waypointAdded(map_pos.x(), map_pos.y());
}

void MapWidget::handleMarkerPlacement(const QPointF &map_pos)
{
    MapMarker marker;
    marker.id = QString("marker_%1").arg(QDateTime::currentMSecsSinceEpoch());
    marker.name = QString("标记点 %1").arg(markers_.size() + 1);
    marker.position = map_pos;
    marker.color = Qt::red;
    marker.icon_type = "circle";
    marker.description = "";
    marker.visible = true;

    showMarkerDialog(marker, true);
}

// 查找标记点
MapMarker* MapWidget::findMarkerAt(const QPointF &widget_pos)
{
    QPointF map_pos = widgetToMap(widget_pos);
    double threshold = MARKER_SIZE / scale_;  // 根据缩放调整点击阈值

    for (auto &marker : markers_) {
        if (marker.visible) {
            double distance = QLineF(marker.position, map_pos).length();
            if (distance <= threshold) {
                return &marker;
            }
        }
    }
    return nullptr;
}

// 标记点对话框
void MapWidget::showMarkerDialog(MapMarker &marker, bool is_new)
{
    bool ok;
    QString name = QInputDialog::getText(this,
        is_new ? "添加标记点" : "编辑标记点",
        "标记点名称:", QLineEdit::Normal, marker.name, &ok);

    if (ok && !name.isEmpty()) {
        marker.name = name;

        // 选择颜色
        QColor color = QColorDialog::getColor(marker.color, this, "选择标记点颜色");
        if (color.isValid()) {
            marker.color = color;
        }

        if (is_new) {
            addMarker(marker);
        } else {
            updateMarker(marker.id, marker);
        }
    }
}

// 设置交互模式
void MapWidget::setInteractionMode(int mode)
{
    interaction_mode_ = static_cast<InteractionMode>(mode);

    // 更新鼠标光标
    switch (interaction_mode_) {
        case SET_GOAL:
            setCursor(Qt::CrossCursor);
            break;
        case ADD_WAYPOINT:
            setCursor(Qt::PointingHandCursor);
            break;
        case ADD_MARKER:
            setCursor(Qt::CrossCursor);
            break;
        default:
            setCursor(Qt::ArrowCursor);
            break;
    }
}

// 数据持久化函数
void MapWidget::saveMarkersToFile(const QString &filename)
{
    QJsonDocument doc;
    QJsonObject root;
    QJsonArray markers_array;

    for (const auto &marker : markers_) {
        QJsonObject marker_obj;
        marker_obj["id"] = marker.id;
        marker_obj["name"] = marker.name;
        marker_obj["position_x"] = marker.position.x();
        marker_obj["position_y"] = marker.position.y();
        marker_obj["color"] = marker.color.name();
        marker_obj["icon_type"] = marker.icon_type;
        marker_obj["description"] = marker.description;
        marker_obj["visible"] = marker.visible;

        markers_array.append(marker_obj);
    }

    root["markers"] = markers_array;
    root["version"] = "1.0";
    root["created"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    doc.setObject(root);

    QFile file(filename);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
        qDebug() << "标记点已保存到:" << filename;
    } else {
        qWarning() << "无法保存标记点文件:" << filename;
    }
}

void MapWidget::loadMarkersFromFile(const QString &filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开标记点文件:" << filename;
        return;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        qWarning() << "解析标记点文件失败:" << error.errorString();
        return;
    }

    QJsonObject root = doc.object();
    QJsonArray markers_array = root["markers"].toArray();

    markers_.clear();

    for (const auto &value : markers_array) {
        QJsonObject marker_obj = value.toObject();

        MapMarker marker;
        marker.id = marker_obj["id"].toString();
        marker.name = marker_obj["name"].toString();
        marker.position.setX(marker_obj["position_x"].toDouble());
        marker.position.setY(marker_obj["position_y"].toDouble());
        marker.color = QColor(marker_obj["color"].toString());
        marker.icon_type = marker_obj["icon_type"].toString();
        marker.description = marker_obj["description"].toString();
        marker.visible = marker_obj["visible"].toBool();

        markers_.append(marker);
    }

    update();
    qDebug() << "已加载" << markers_.size() << "个标记点从:" << filename;
}

} // namespace robotcar_gui
